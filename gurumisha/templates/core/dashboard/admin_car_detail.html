{% extends 'base_admin_dashboard.html' %}
{% load static %}
{% load core_extras %}

{% block dashboard_title %}Car Details{% endblock %}
{% block page_title %}{{ car.title }}{% endblock %}
{% block page_description %}{{ car.brand.name }} {{ car.model.name }} • {{ car.year }}{% endblock %}

{% block dashboard_content %}
    {% csrf_token %}

    <!-- Test Content -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h1 class="text-2xl font-bold text-harrier-dark mb-4">Car Details - {{ car.title }}</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-semibold text-gray-700 mb-2">Basic Information</h3>
                <p><strong>Brand:</strong> {{ car.brand.name }}</p>
                <p><strong>Model:</strong> {{ car.model.name }}</p>
                <p><strong>Year:</strong> {{ car.year }}</p>
                <p><strong>Price:</strong> {{ car.price|currency_ksh_no_decimals }}</p>
                <p><strong>Status:</strong>
                    {% if car.is_approved %}
                        <span class="text-green-600">Approved</span>
                    {% else %}
                        <span class="text-yellow-600">Pending</span>
                    {% endif %}
                </p>
            </div>
            <div>
                <h3 class="font-semibold text-gray-700 mb-2">Vendor Information</h3>
                <p><strong>Company:</strong> {{ car.vendor.company_name }}</p>
                <p><strong>Contact:</strong> {{ car.vendor.user.email }}</p>
                <p><strong>Phone:</strong> {{ car.vendor.phone|default:"Not provided" }}</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 flex flex-wrap gap-4">
            <!-- Hot Deals Button -->
            {% if car.is_hot_deal and car.hot_deal_details %}
                <button onclick="manageHotDeal({{ car.id }})"
                        class="bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 shadow-lg flex items-center space-x-2 font-semibold">
                    <i class="fas fa-fire"></i>
                    <span>Manage Hot Deal</span>
                    <span class="bg-white bg-opacity-20 px-2 py-1 rounded text-xs">
                        {{ car.hot_deal_details.discount_value }}% OFF
                    </span>
                </button>
            {% else %}
                <button onclick="createHotDeal({{ car.id }})"
                        class="bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-red-700 transition-all duration-300 shadow-lg flex items-center space-x-2 font-semibold">
                    <i class="fas fa-fire"></i>
                    <span>Create Hot Deal</span>
                </button>
            {% endif %}

            <button onclick="editCar({{ car.id }})"
                    class="bg-gradient-to-r from-harrier-red to-harrier-dark text-white px-6 py-3 rounded-lg hover:from-harrier-dark hover:to-black transition-all duration-300 shadow-lg flex items-center space-x-2 font-semibold">
                <i class="fas fa-edit"></i>
                <span>Edit Details</span>
            </button>

            <a href="{% url 'core:admin_listings' %}"
               class="bg-gradient-to-r from-gray-500 to-gray-600 text-white px-6 py-3 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg flex items-center space-x-2 font-semibold">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Listings</span>
            </a>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column - Car Details -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Car Images -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden admin-card glassmorphism">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-harrier-red to-harrier-dark">
                    <h2 class="text-xl font-bold text-white font-montserrat flex items-center">
                        <i class="fas fa-images text-yellow-300 mr-3"></i>Car Images
                    </h2>
                </div>
                <div class="p-6">
                    {% if car.main_image %}
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-700 mb-3">Main Image</h3>
                            <img src="{{ car.main_image.url }}" alt="{{ car.title }}" 
                                 class="w-full h-64 object-cover rounded-lg shadow-md">
                        </div>
                    {% endif %}
                    
                    {% if car_images %}
                        <div>
                            <h3 class="text-lg font-semibold text-gray-700 mb-3">Gallery Images</h3>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                {% for image in car_images %}
                                    <img src="{{ image.image.url }}" alt="{{ image.caption|default:car.title }}" 
                                         class="w-full h-32 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow">
                                {% endfor %}
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-image text-4xl mb-4"></i>
                            <p>No additional images available</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Car Specifications -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat flex items-center">
                        <i class="fas fa-cogs text-harrier-red mr-3"></i>Specifications
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Brand</span>
                                <span class="text-harrier-dark font-semibold">{{ car.brand.name }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Model</span>
                                <span class="text-harrier-dark font-semibold">{{ car.model.name }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Year</span>
                                <span class="text-harrier-dark font-semibold">{{ car.year }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Condition</span>
                                <span class="text-harrier-dark font-semibold">{{ car.get_condition_display }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Mileage</span>
                                <span class="text-harrier-dark font-semibold">{{ car.mileage|floatformat:0 }} km</span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Fuel Type</span>
                                <span class="text-harrier-dark font-semibold">{{ car.get_fuel_type_display }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Transmission</span>
                                <span class="text-harrier-dark font-semibold">{{ car.get_transmission_display }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Engine Size</span>
                                <span class="text-harrier-dark font-semibold">{{ car.engine_size }}L</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Color</span>
                                <span class="text-harrier-dark font-semibold">{{ car.color }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-100">
                                <span class="font-medium text-gray-600">Listing Type</span>
                                <span class="text-harrier-dark font-semibold">{{ car.get_listing_type_display }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            {% if car.description %}
            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat flex items-center">
                        <i class="fas fa-file-alt text-harrier-red mr-3"></i>Description
                    </h2>
                </div>
                <div class="p-6">
                    <p class="text-gray-700 leading-relaxed">{{ car.description|linebreaks }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Features -->
            {% if features_list %}
            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat flex items-center">
                        <i class="fas fa-list-check text-harrier-red mr-3"></i>Features
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {% for feature in features_list %}
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-700">{{ feature }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Right Column - Vendor Info & Actions -->
        <div class="space-y-8">
            <!-- Vendor Information -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat flex items-center">
                        <i class="fas fa-store text-harrier-red mr-3"></i>Vendor Information
                    </h2>
                </div>
                <div class="p-6">
                    <div class="text-center mb-6">
                        {% if car.vendor.logo %}
                            <img src="{{ car.vendor.logo.url }}" alt="{{ car.vendor.company_name }}" 
                                 class="w-20 h-20 object-cover rounded-full mx-auto mb-4 border-4 border-gray-200">
                        {% else %}
                            <div class="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                                <i class="fas fa-store text-gray-400 text-2xl"></i>
                            </div>
                        {% endif %}
                        <h3 class="text-lg font-bold text-harrier-dark">{{ car.vendor.company_name }}</h3>
                        <p class="text-gray-600">{{ car.vendor.user.get_full_name|default:car.vendor.user.username }}</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-gray-400 mr-3"></i>
                            <span class="text-gray-700">{{ car.vendor.user.email }}</span>
                        </div>
                        {% if car.vendor.phone %}
                        <div class="flex items-center">
                            <i class="fas fa-phone text-gray-400 mr-3"></i>
                            <span class="text-gray-700">{{ car.vendor.phone }}</span>
                        </div>
                        {% endif %}
                        {% if car.vendor.location %}
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt text-gray-400 mr-3"></i>
                            <span class="text-gray-700">{{ car.vendor.location }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Car Statistics -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat flex items-center">
                        <i class="fas fa-chart-bar text-harrier-red mr-3"></i>Statistics
                    </h2>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Views</span>
                        <span class="text-2xl font-bold text-harrier-red">{{ car.views_count }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Listed Date</span>
                        <span class="text-gray-700">{{ car.created_at|date:"M d, Y" }}</span>
                    </div>
                    {% if car.approval_date %}
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Approved Date</span>
                        <span class="text-gray-700">{{ car.approval_date|date:"M d, Y" }}</span>
                    </div>
                    {% endif %}
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Inquiries</span>
                        <span class="text-2xl font-bold text-blue-600">{{ car_inquiries.count }}</span>
                    </div>
                </div>
            </div>

            <!-- Hot Deal Status -->
            {% if car.is_hot_deal and car.hot_deal_details %}
                <div class="bg-gradient-to-br from-red-50 to-orange-50 rounded-xl shadow-lg border-2 border-red-200 overflow-hidden">
                    <div class="p-6 border-b border-red-200 bg-gradient-to-r from-red-600 to-red-700">
                        <h2 class="text-xl font-bold text-white font-montserrat flex items-center">
                            <i class="fas fa-fire text-yellow-300 mr-3 animate-pulse"></i>Active Hot Deal
                        </h2>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-red-600">{{ car.hot_deal_details.discount_value }}%</div>
                                <div class="text-sm text-gray-600">Discount</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
                                <div class="text-lg font-bold text-green-600">KSh {{ car.hot_deal_details.discounted_price|floatformat:0 }}</div>
                                <div class="text-sm text-gray-600">Sale Price</div>
                            </div>
                        </div>

                        <!-- Countdown Timer -->
                        <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                            <div class="text-sm text-gray-600 mb-2">Time Remaining</div>
                            <div class="countdown-timer text-lg font-bold text-red-600"
                                 data-countdown-end="{{ car.hot_deal_details.end_date|date:'c' }}">
                                <span id="countdown-{{ car.id }}">Loading...</span>
                            </div>
                        </div>

                        <div class="text-center">
                            <button onclick="manageHotDeal({{ car.id }})"
                                    class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-2 px-4 rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 font-semibold">
                                <i class="fas fa-cog mr-2"></i>Manage Deal
                            </button>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Admin Actions -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat flex items-center">
                        <i class="fas fa-tools text-harrier-red mr-3"></i>Admin Actions
                    </h2>
                </div>
                <div class="p-6 space-y-4">
                    {% if not car.is_approved %}
                        <button onclick="approveCar({{ car.id }})"
                                class="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg transform hover:scale-105">
                            <i class="fas fa-check mr-2"></i>Approve Listing
                        </button>
                        <button onclick="rejectCar({{ car.id }})"
                                class="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg transform hover:scale-105">
                            <i class="fas fa-times mr-2"></i>Reject Listing
                        </button>
                    {% else %}
                        <div class="bg-gradient-to-br from-green-50 to-green-100 border-2 border-green-200 rounded-xl p-6 text-center shadow-lg">
                            <div class="bg-green-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-check-circle text-white text-2xl"></i>
                            </div>
                            <p class="text-green-800 font-bold text-lg mb-1">Listing Approved</p>
                            <p class="text-green-600 text-sm">{{ car.approval_date|date:"M d, Y" }}</p>
                        </div>
                    {% endif %}
                    
                    <button onclick="editCar({{ car.id }})"
                            class="w-full bg-gradient-to-r from-harrier-red to-harrier-dark hover:from-harrier-dark hover:to-black text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg transform hover:scale-105">
                        <i class="fas fa-edit mr-2"></i>Edit Details
                    </button>
                    
                    {% if not car.is_currently_featured %}
                        <button onclick="featureCar({{ car.id }})"
                                id="feature-btn-{{ car.id }}"
                                class="w-full bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg transform hover:scale-105">
                            <i class="fas fa-star mr-2"></i>Mark as Featured
                        </button>
                    {% else %}
                        <button onclick="unfeatureCar({{ car.id }})"
                                id="feature-btn-{{ car.id }}"
                                class="w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg">
                            <i class="fas fa-star mr-2"></i>Remove Featured
                        </button>
                    {% endif %}

                    <button onclick="deleteCar({{ car.id }})"
                            class="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg transform hover:scale-105">
                        <i class="fas fa-trash mr-2"></i>Delete Car Listing
                    </button>

                    <a href="{% url 'core:admin_listings' %}"
                       class="w-full bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors inline-block text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Listings
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
function editCar(carId) {
    // Load edit modal via HTMX
    fetch(`/dashboard/admin/car/${carId}/edit/`)
        .then(response => response.text())
        .then(html => {
            // Create modal container
            const modalContainer = document.createElement('div');
            modalContainer.innerHTML = html;
            document.body.appendChild(modalContainer);
        })
        .catch(error => {
            console.error('Error loading edit modal:', error);
            alert('An error occurred while loading the edit form.');
        });
}

function featureCar(carId) {
    const button = document.getElementById(`feature-btn-${carId}`);
    if (confirm('Are you sure you want to mark this car as featured?')) {
        // Disable button and show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Featuring...';

        fetch(`/dashboard/admin/car/${carId}/feature/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=feature'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update button to unfeature state
                button.className = 'w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors';
                button.innerHTML = '<i class="fas fa-star mr-2"></i>Remove Featured';
                button.onclick = () => unfeatureCar(carId);
                button.disabled = false;

                // Show success notification
                if (window.toastManager) {
                    window.toastManager.show(data.message, 'success', {
                        duration: 5000
                    });
                }
            } else {
                // Reset button on error
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-star mr-2"></i>Mark as Featured';

                if (window.toastManager) {
                    window.toastManager.show(data.message, 'error', {
                        duration: 5000
                    });
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.showError('An error occurred while featuring the car.');
        });
    }
}

function unfeatureCar(carId) {
    const button = document.getElementById(`feature-btn-${carId}`);
    if (confirm('Are you sure you want to remove the featured status from this car?')) {
        // Disable button and show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Removing...';

        fetch(`/dashboard/admin/car/${carId}/feature/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=unfeature'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update button to feature state
                button.className = 'w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors';
                button.innerHTML = '<i class="fas fa-star mr-2"></i>Mark as Featured';
                button.onclick = () => featureCar(carId);
                button.disabled = false;

                // Show success notification
                if (window.toastManager) {
                    window.toastManager.show(data.message, 'success', {
                        duration: 5000
                    });
                }
            } else {
                // Reset button on error
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-star mr-2"></i>Remove Featured';

                if (window.toastManager) {
                    window.toastManager.show(data.message, 'error', {
                        duration: 5000
                    });
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.showError('An error occurred while unfeaturing the car.');
        });
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
        'bg-blue-500'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => toast.style.transform = 'translateX(0)', 10);

    // Remove after 5 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 5000);
}

function deleteCar(carId) {
    const carTitle = document.querySelector('h1').textContent.trim();

    // Enhanced confirmation dialog
    const confirmMessage = `Are you sure you want to permanently delete "${carTitle}"?\n\nThis action cannot be undone and will:\n• Remove the car from all listings\n• Delete all associated images\n• Remove from featured cars (if applicable)\n• Cancel any active inquiries\n\nType "DELETE" to confirm:`;

    const userInput = prompt(confirmMessage);

    if (userInput === 'DELETE') {
        const button = document.querySelector(`button[onclick="deleteCar(${carId})"]`);

        // Disable button and show loading
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Deleting...';

        fetch(`/dashboard/admin/car/${carId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Show success notification
                window.toastManager.show(
                    `Car "${carTitle}" has been permanently deleted`,
                    'success',
                    {
                        duration: 3000,
                        action: {
                            text: 'Go to Listings',
                            handler: () => {
                                window.location.href = '/dashboard/admin/listings/';
                            }
                        }
                    }
                );

                // Redirect to listings after a short delay
                setTimeout(() => {
                    window.location.href = '/dashboard/admin/listings/';
                }, 2000);

            } else {
                window.showError(data.message || 'Failed to delete car listing');
                // Reset button
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-trash mr-2"></i>Delete Car Listing';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.showError('An error occurred while deleting the car listing.');
            // Reset button
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-trash mr-2"></i>Delete Car Listing';
        });
    }
}

// Hot Deal Management Functions
function createHotDeal(carId) {
    // Show hot deal creation modal
    const modalHtml = `
        <div class="fixed inset-0 z-[1500] overflow-y-auto" id="hot-deal-modal">
            <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
            <div class="flex min-h-full items-center justify-center p-4">
                <div class="relative bg-white rounded-xl shadow-xl max-w-md w-full">
                    <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-red-600 to-red-700">
                        <h3 class="text-xl font-bold text-white flex items-center">
                            <i class="fas fa-fire mr-3"></i>Create Hot Deal
                        </h3>
                    </div>
                    <form id="hot-deal-form" class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-percentage mr-1"></i>Discount Percentage
                            </label>
                            <input type="number" id="discount-value" min="5" max="50" value="10"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar mr-1"></i>Duration (Days)
                            </label>
                            <input type="number" id="deal-days" min="1" max="30" value="7"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                        <div class="flex space-x-3 pt-4">
                            <button type="button" onclick="submitHotDeal(${carId})"
                                    class="flex-1 bg-gradient-to-r from-red-600 to-red-700 text-white py-2 px-4 rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 font-semibold">
                                <i class="fas fa-fire mr-2"></i>Create Deal
                            </button>
                            <button type="button" onclick="closeHotDealModal()"
                                    class="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function manageHotDeal(carId) {
    // Show hot deal management options
    const modalHtml = `
        <div class="fixed inset-0 z-[1500] overflow-y-auto" id="hot-deal-modal">
            <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
            <div class="flex min-h-full items-center justify-center p-4">
                <div class="relative bg-white rounded-xl shadow-xl max-w-md w-full">
                    <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-red-600 to-red-700">
                        <h3 class="text-xl font-bold text-white flex items-center">
                            <i class="fas fa-fire mr-3"></i>Manage Hot Deal
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <button onclick="extendHotDeal(${carId})"
                                class="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-4 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-300 font-semibold">
                            <i class="fas fa-clock mr-2"></i>Extend Deal
                        </button>
                        <button onclick="editHotDeal(${carId})"
                                class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-4 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-semibold">
                            <i class="fas fa-edit mr-2"></i>Edit Deal
                        </button>
                        <button onclick="endHotDeal(${carId})"
                                class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 font-semibold">
                            <i class="fas fa-stop mr-2"></i>End Deal
                        </button>
                        <button onclick="closeHotDealModal()"
                                class="w-full bg-gray-500 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function submitHotDeal(carId) {
    const discountValue = document.getElementById('discount-value').value;
    const dealDays = document.getElementById('deal-days').value;

    if (!discountValue || !dealDays) {
        window.showError('Please fill in all fields.');
        return;
    }

    fetch(`/dashboard/admin/car/${carId}/hot-deal/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'create',
            discount_value: discountValue,
            days: dealDays
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            window.showSuccess(data.message);
            closeHotDealModal();
            location.reload(); // Refresh to show hot deal status
        } else {
            window.showError(data.message || 'Failed to create hot deal.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        window.showError('An error occurred while creating the hot deal.');
    });
}

function endHotDeal(carId) {
    if (confirm('Are you sure you want to end this hot deal?')) {
        fetch(`/dashboard/admin/car/${carId}/hot-deal/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'end'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                window.showSuccess(data.message);
                closeHotDealModal();
                location.reload();
            } else {
                window.showError(data.message || 'Failed to end hot deal.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.showError('An error occurred while ending the hot deal.');
        });
    }
}

function closeHotDealModal() {
    const modal = document.getElementById('hot-deal-modal');
    if (modal) {
        modal.remove();
    }
}

// Countdown Timer Function
function initCountdownTimer() {
    const timers = document.querySelectorAll('.countdown-timer');
    timers.forEach(timer => {
        const endDate = timer.getAttribute('data-countdown-end');
        if (endDate) {
            updateCountdown(timer, endDate);
            setInterval(() => updateCountdown(timer, endDate), 1000);
        }
    });
}

function updateCountdown(element, endDate) {
    const now = new Date().getTime();
    const end = new Date(endDate).getTime();
    const distance = end - now;

    if (distance > 0) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        element.innerHTML = `${days}d ${hours}h ${minutes}m ${seconds}s`;

        if (distance < 86400000) { // Less than 24 hours
            element.classList.add('text-red-600', 'animate-pulse');
        }
    } else {
        element.innerHTML = "EXPIRED";
        element.classList.add('text-red-600', 'font-bold');
    }
}

// Initialize countdown timers when page loads
document.addEventListener('DOMContentLoaded', function() {
    initCountdownTimer();
});

// Enhanced approve/reject functions with toast notifications
function approveCar(carId) {
    if (confirm('Are you sure you want to approve this car listing?')) {
        // Show loading state
        const approveBtn = document.querySelector(`button[onclick="approveCar(${carId})"]`);
        if (approveBtn) {
            approveBtn.disabled = true;
            approveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Approving...';
        }

        fetch(`/dashboard/admin/approve-car/${carId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
                'HX-Request': 'true'
            },
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw new Error('Network response was not ok');
        })
        .then(data => {
            if (data.status === 'approved') {
                // Show success toast
                showToast('Car listing approved successfully!', 'success');
                // Reload page to show updated status
                setTimeout(() => location.reload(), 1000);
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error approving car listing. Please try again.', 'error');
            // Reset button state
            if (approveBtn) {
                approveBtn.disabled = false;
                approveBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Approve Listing';
            }
        });
    }
}

function rejectCar(carId) {
    if (confirm('Are you sure you want to reject this car listing? This action cannot be undone.')) {
        // Show loading state
        const rejectBtn = document.querySelector(`button[onclick="rejectCar(${carId})"]`);
        if (rejectBtn) {
            rejectBtn.disabled = true;
            rejectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Rejecting...';
        }

        fetch(`/dashboard/admin/reject-car/${carId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
                'HX-Request': 'true'
            },
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw new Error('Network response was not ok');
        })
        .then(data => {
            if (data.status === 'rejected') {
                // Show success toast
                showToast('Car listing rejected successfully!', 'success');
                // Redirect to listings page
                setTimeout(() => window.location.href = '/dashboard/admin/listings/', 1000);
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error rejecting car listing. Please try again.', 'error');
            // Reset button state
            if (rejectBtn) {
                rejectBtn.disabled = false;
                rejectBtn.innerHTML = '<i class="fas fa-times mr-2"></i>Reject Listing';
            }
        });
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg text-white font-semibold transform transition-all duration-300 translate-x-full`;

    // Set color based on type
    if (type === 'success') {
        toast.classList.add('bg-green-600');
    } else if (type === 'error') {
        toast.classList.add('bg-red-600');
    } else {
        toast.classList.add('bg-blue-600');
    }

    toast.innerHTML = `
        <div class="flex items-center space-x-3">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}
</script>

<style>
/* Enhanced Admin Car Detail Page Styling */
.glassmorphism {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glassmorphism-dark {
    backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced button hover effects */
.enhanced-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.enhanced-btn:hover::before {
    left: 100%;
}

/* Hot deal animation */
@keyframes hotDealPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
}

.hot-deal-active {
    animation: hotDealPulse 2s infinite;
}

/* Countdown timer styling */
.countdown-timer {
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

/* Card hover effects */
.admin-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Enhanced gradient backgrounds */
.gradient-red-black {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #1f2937 100%);
}

.gradient-orange-red {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
}

/* Status indicators */
.status-indicator {
    position: relative;
    display: inline-block;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: statusPulse 2s infinite;
}

.status-approved::after {
    background-color: #10b981;
}

.status-pending::after {
    background-color: #f59e0b;
}

.status-hot-deal::after {
    background-color: #ef4444;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .admin-card {
        margin-bottom: 1rem;
    }

    .enhanced-btn {
        transform: none !important;
    }

    .enhanced-btn:hover {
        transform: none !important;
    }
}
</style>
{% endblock %}
