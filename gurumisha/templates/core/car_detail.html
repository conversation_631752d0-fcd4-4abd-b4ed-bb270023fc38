{% extends 'base.html' %}
{% load static %}
{% load core_extras %}

{% block title %}{{ car.title }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
/* Enhanced Car Detail Page Styles with Harrier Design */
.car-detail-hero {
    background: linear-gradient(135deg, #DC2626 0%, #1F2937 50%, #1E3A8A 100%);
    position: relative;
    overflow: hidden;
}

.car-detail-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.glassmorphism {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.image-gallery {
    position: relative;
    border-radius: 1.5rem;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;
}

.image-gallery:hover {
    transform: translateY(-4px);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

.main-image {
    height: 500px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.main-image:hover {
    transform: scale(1.02);
}

.thumbnail-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.thumbnail {
    height: 80px;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
}

.thumbnail:hover {
    border-color: #DC2626;
    transform: scale(1.05);
}

.thumbnail.active {
    border-color: #DC2626;
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.car-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 10;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    backdrop-filter: blur(10px);
}

.badge-featured {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    color: white;
}

.badge-certified {
    background: linear-gradient(135deg, #10B981, #34D399);
    color: white;
}

.badge-new {
    background: linear-gradient(135deg, #3B82F6, #60A5FA);
    color: white;
}

.badge-hot-deal {
    background: linear-gradient(135deg, #F59E0B, #FBBF24);
    color: white;
}

.car-info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.car-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.2);
}

.location-item {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 1rem;
}

.location-item:hover {
    background: rgba(220, 38, 38, 0.05);
    transform: translateY(-1px);
}

.price-display {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 3rem;
    font-weight: 800;
    font-family: 'Montserrat', sans-serif;
    margin-bottom: 1rem;
}

.spec-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.spec-table tr:nth-child(even) {
    background: rgba(243, 244, 246, 0.5);
}

.spec-table tr:nth-child(odd) {
    background: rgba(255, 255, 255, 0.8);
}

.spec-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    font-family: 'Raleway', sans-serif;
}

.spec-table .label-spec {
    font-weight: 600;
    color: #374151;
    width: 40%;
}

.spec-table .value-spec {
    color: #1F2937;
    font-weight: 500;
}

.action-button {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    color: white;
    padding: 1rem 2rem;
    border-radius: 1rem;
    font-weight: 700;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px -5px rgba(220, 38, 38, 0.4);
    background: linear-gradient(135deg, #B91C1C, #DC2626);
}

.action-button-secondary {
    background: linear-gradient(135deg, #1E3A8A, #3B82F6);
    color: white;
}

.action-button-secondary:hover {
    background: linear-gradient(135deg, #1E40AF, #2563EB);
    box-shadow: 0 15px 30px -5px rgba(30, 58, 138, 0.4);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }

@media (max-width: 768px) {
    .price-display {
        font-size: 2rem;
    }

    .main-image {
        height: 300px;
    }

    .car-info-card {
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Hidden CSRF Token for JavaScript -->
{% csrf_token %}

<!-- Breadcrumb -->
<div class="bg-harrier-gray py-4">
    <div class="container mx-auto px-4">
        <nav class="text-sm">
            <ol class="flex items-center space-x-2">
                <li><a href="{% url 'core:homepage' %}" class="text-harrier-dark hover:text-harrier-red">Home</a></li>
                <li class="text-gray-500">&rsaquo;</li>
                <li><a href="{% url 'core:car_list' %}" class="text-harrier-dark hover:text-harrier-red">Cars</a></li>
                <li class="text-gray-500">&rsaquo;</li>
                <li><a href="{% url 'core:car_list' %}?brand={{ car.brand.id }}" class="text-harrier-dark hover:text-harrier-red">{{ car.brand.name }}</a></li>
                <li class="text-gray-500">&rsaquo;</li>
                <li class="text-harrier-red font-semibold">{{ car.model.name }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Enhanced Car Detail Section -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Enhanced Image Gallery -->
            <div class="animate-fade-in-up">
                <div class="image-gallery">
                    <!-- Car Badges -->
                    <div class="car-badge">
                        {% if car.is_featured %}
                            <span class="badge badge-featured">
                                <i class="fas fa-star mr-1"></i>Featured
                            </span>
                        {% endif %}
                        {% if car.is_certified %}
                            <span class="badge badge-certified">
                                <i class="fas fa-shield-check mr-1"></i>Certified
                            </span>
                        {% endif %}
                        {% if car.condition == 'new' %}
                            <span class="badge badge-new">
                                <i class="fas fa-sparkles mr-1"></i>New
                            </span>
                        {% endif %}
                        {% if car.is_hot_deal %}
                            <span class="badge badge-hot-deal">
                                <i class="fas fa-fire mr-1"></i>Hot Deal
                            </span>
                        {% endif %}
                    </div>

                    <!-- Main Image -->
                    <div class="relative">
                        {% if car.main_image %}
                            <img src="{{ car.main_image.url }}" alt="{{ car.title }}"
                                 class="w-full main-image" id="main-image">
                        {% else %}
                            <div class="w-full main-image bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <i class="fas fa-car text-gray-400 text-8xl"></i>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Enhanced Thumbnail Gallery -->
                    {% if car.images.all or car.main_image %}
                        <div class="thumbnail-gallery">
                            {% if car.main_image %}
                                <img src="{{ car.main_image.url }}" alt="{{ car.title }}"
                                     class="thumbnail active object-cover"
                                     onclick="changeMainImage('{{ car.main_image.url }}', this)">
                            {% endif %}
                            {% for image in car.images.all %}
                                <img src="{{ image.image.url }}" alt="{{ image.caption }}"
                                     class="thumbnail object-cover"
                                     onclick="changeMainImage('{{ image.image.url }}', this)">
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Enhanced Contact Actions -->
                <div class="glassmorphism rounded-2xl p-6 mt-8">
                    <h3 class="text-xl font-bold text-harrier-dark mb-4 font-montserrat">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="tel:+254700000000" class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <div class="font-semibold text-harrier-dark font-montserrat">Call Now</div>
                                <div class="text-sm text-gray-600 font-raleway">+254 700 000 000</div>
                            </div>
                        </a>
                        <a href="#" onclick="openInquiryModal()" class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <div class="font-semibold text-harrier-dark font-montserrat">Send Message</div>
                                <div class="text-sm text-gray-600 font-raleway">Ask a question</div>
                            </div>
                        </a>
                        <a href="{% url 'core:car_calculator' %}?car_id={{ car.id }}" class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div>
                                <div class="font-semibold text-harrier-dark font-montserrat">Finance Calculator</div>
                                <div class="text-sm text-gray-600 font-raleway">Calculate payments</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Car Information -->
            <div class="animate-fade-in-up animate-delay-200">
                <div class="car-info-card">
                    <!-- Brand Badge -->
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-harrier-red to-red-600 text-white rounded-full text-sm font-bold uppercase tracking-wide mb-4 font-montserrat">
                        <i class="fas fa-award mr-2"></i>{{ car.brand.name }}
                    </div>

                    <!-- Car Title -->
                    <h1 class="text-4xl lg:text-5xl font-bold text-harrier-dark mb-6 font-montserrat leading-tight">
                        {{ car.title }}
                    </h1>

                    <!-- Rating and Views -->
                    <div class="flex items-center mb-6">
                        <div class="flex text-yellow-400 mr-4">
                            {% for i in "12345" %}
                                {% if forloop.counter <= car.calculated_rating|default:4 %}
                                    <i class="fas fa-star text-lg"></i>
                                {% else %}
                                    <i class="far fa-star text-lg"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <div class="text-gray-600 font-raleway">
                            <span class="font-semibold">{{ car.calculated_rating|default:"4.0"|floatformat:1 }}</span>
                            <span class="mx-2">•</span>
                            <span>{{ car.views_count }} view{{ car.views_count|pluralize }}</span>
                        </div>
                    </div>

                    <!-- Enhanced Price Display -->
                    <div class="mb-8">
                        <div class="flex items-baseline space-x-2 mb-2">
                            <span class="text-lg text-gray-600 font-raleway">Starting from</span>
                        </div>
                        <div class="price-display">
                            {{ car.price|currency_ksh_no_decimals }}
                        </div>
                        {% if car.is_hot_deal %}
                            <div class="flex items-center text-orange-600 font-semibold font-raleway">
                                <i class="fas fa-fire mr-2"></i>
                                <span>Hot Deal - Limited Time Offer!</span>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Enhanced Location Information with Red-to-Black Theme and Glassmorphism -->
                    {% if car.area or car.city or car.country %}
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6 font-montserrat flex items-center">
                            <i class="fas fa-map-marker-alt text-red-600 mr-3"></i>
                            Vehicle Location
                        </h2>
                        <div class="bg-white/95 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {% if car.area %}
                                <div class="location-item group">
                                    <div class="flex items-center mb-2">
                                        <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-gray-900 rounded-xl flex items-center justify-center mr-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <i class="fas fa-building text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm text-gray-500 font-raleway">Area/Neighborhood</div>
                                            <div class="text-lg font-bold text-gray-900 font-montserrat">{{ car.area }}</div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                {% if car.city %}
                                <div class="location-item group">
                                    <div class="flex items-center mb-2">
                                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-gray-800 rounded-xl flex items-center justify-center mr-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <i class="fas fa-city text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm text-gray-500 font-raleway">City</div>
                                            <div class="text-lg font-bold text-gray-900 font-montserrat">{{ car.city }}</div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                {% if car.country %}
                                <div class="location-item group">
                                    <div class="flex items-center mb-2">
                                        <div class="w-12 h-12 bg-gradient-to-br from-red-700 to-black rounded-xl flex items-center justify-center mr-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <i class="fas fa-globe text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm text-gray-500 font-raleway">Country</div>
                                            <div class="text-lg font-bold text-gray-900 font-montserrat">{{ car.country }}</div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>

                            <!-- Full Address Display with Enhanced Styling -->
                            <div class="mt-6 pt-6 border-t border-gray-200/50">
                                <div class="bg-gradient-to-r from-red-50 to-gray-50 rounded-xl p-4 border border-red-100">
                                    <div class="flex items-center text-gray-700 font-raleway">
                                        <div class="w-8 h-8 bg-gradient-to-br from-red-600 to-gray-900 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-map-pin text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs text-gray-500 uppercase tracking-wide font-medium">Complete Address</div>
                                            <div class="text-sm font-semibold text-gray-900">
                                                {% if car.area %}{{ car.area }}{% if car.city %}, {% endif %}{% endif %}
                                                {% if car.city %}{{ car.city }}{% if car.country %}, {% endif %}{% endif %}
                                                {% if car.country %}{{ car.country }}{% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Enhanced Specifications -->
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-harrier-dark mb-6 font-montserrat">Vehicle Specifications</h2>
                        <table class="spec-table">
                            <tbody>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-road text-harrier-red mr-2"></i>Mileage
                                    </td>
                                    <td class="value-spec">{{ car.mileage|floatformat:0|default:"N/A" }} km</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-cogs text-harrier-red mr-2"></i>Engine Size
                                    </td>
                                    <td class="value-spec">{{ car.engine_size|default:"N/A" }}</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-gear text-harrier-red mr-2"></i>Transmission
                                    </td>
                                    <td class="value-spec">{{ car.get_transmission_display }}</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-gas-pump text-harrier-red mr-2"></i>Fuel Type
                                    </td>
                                    <td class="value-spec">{{ car.get_fuel_type_display }}</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-calendar text-harrier-red mr-2"></i>Model Year
                                    </td>
                                    <td class="value-spec">{{ car.year }}</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-palette text-harrier-red mr-2"></i>Color
                                    </td>
                                    <td class="value-spec">{{ car.color|title }}</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-star text-harrier-red mr-2"></i>Condition
                                    </td>
                                    <td class="value-spec">{{ car.condition|title }}</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-car text-harrier-red mr-2"></i>Body Type
                                    </td>
                                    <td class="value-spec">{{ car.body_type|default:"N/A"|title }}</td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-building text-harrier-red mr-2"></i>Dealer
                                    </td>
                                    <td class="value-spec">
                                        <a href="{% url 'core:dealer_profile' car.vendor.id %}" class="text-harrier-red hover:text-red-600 font-semibold">
                                            {{ car.vendor.company_name }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label-spec">
                                        <i class="fas fa-clock text-harrier-red mr-2"></i>Listed Date
                                    </td>
                                    <td class="value-spec">{{ car.created_at|date:"M d, Y" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Enhanced Action Buttons -->
                    <div class="space-y-4 mb-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <button onclick="addToWishlist({{ car.id }})" class="action-button-secondary">
                                <i class="fas fa-heart"></i>Add to Wishlist
                            </button>
                            <button onclick="addToCompare({{ car.id }})" class="action-button-secondary">
                                <i class="fas fa-balance-scale"></i>Add to Compare
                            </button>
                        </div>
                        <button onclick="openInquiryModal()" class="action-button w-full justify-center">
                            <i class="fas fa-envelope"></i>Contact Dealer
                        </button>
                    </div>
                </div>
            </div>

                <!-- Social Sharing -->
                <div class="social mb-8">
                    <h4 class="text-sm font-semibold text-harrier-dark mb-3">SHARE THIS CAR:</h4>
                    <ul class="link flex space-x-2">
                        <li class="fb">
                            <a href="#" class="w-10 h-10 bg-blue-600 text-white rounded flex items-center justify-center hover:bg-blue-700 transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        </li>
                        <li class="tw">
                            <a href="#" class="w-10 h-10 bg-blue-400 text-white rounded flex items-center justify-center hover:bg-blue-500 transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                        </li>
                        <li class="googleplus">
                            <a href="#" class="w-10 h-10 bg-red-600 text-white rounded flex items-center justify-center hover:bg-red-700 transition-colors">
                                <i class="fab fa-google-plus-g"></i>
                            </a>
                        </li>
                        <li class="linkedin">
                            <a href="#" class="w-10 h-10 bg-blue-700 text-white rounded flex items-center justify-center hover:bg-blue-800 transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </li>
                        <li class="whatsapp">
                            <a href="#" class="w-10 h-10 bg-green-500 text-white rounded flex items-center justify-center hover:bg-green-600 transition-colors">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Dealer Info Card -->
                {% comment %} <div class="bg-harrier-gray p-6 rounded-lg border">
                    <h3 style="font-family: 'Saira Condensed', sans-serif; font-size: 18px; font-weight: 500; text-transform: uppercase; letter-spacing: 1px; color: #333; margin-bottom: 20px;">DEALER INFORMATION</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <i class="fas fa-building text-harrier-red mr-3"></i>
                            <span class="font-semibold">{{ car.vendor.company_name }}</span>
                        </div>
                        {% if car.vendor.user.phone %}
                            <div class="flex items-center">
                                <i class="fas fa-phone text-harrier-red mr-3"></i>
                                <span>{{ car.vendor.user.phone }}</span>
                            </div>
                        {% endif %}
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-harrier-red mr-3"></i>
                            <span>{{ car.vendor.user.email }}</span>
                        </div>
                        {% if car.vendor.is_approved %}
                            <div class="flex items-center text-green-600">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span class="text-sm font-semibold">Verified Dealer</span>
                            </div>
                        {% endif %}
                        <div class="pt-3 border-t border-gray-300">
                            <a href="{% url 'core:dealer_profile' car.vendor.id %}" class="btn-harrier-secondary w-full text-center block py-2">
                                VIEW DEALER PROFILE
                            </a>
                        </div>
                    </div>
                </div> {% endcomment %}
            </div>
        </div>
    </div>
</section>

<!-- Description and Features -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Description -->
            <div>
                <h2 class="text-2xl font-heading font-bold text-midnight mb-6">Description</h2>
                <div class="prose prose-gray max-w-none">
                    <p>{{ car.description }}</p>
                </div>
            </div>
            
            <!-- Features -->
            <div>
                <h2 class="text-2xl font-heading font-bold text-midnight mb-6">Features</h2>
                {% if car.get_features_list %}
                    <div class="grid grid-cols-1 gap-2">
                        {% for feature in car.get_features_list %}
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span>{{ feature }}</span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500">No specific features listed.</p>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Related Cars -->
{% if related_cars %}
<section class="py-12 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-heading font-bold text-midnight mb-8">Similar Cars</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for related_car in related_cars %}
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="relative">
                        {% if related_car.main_image %}
                            <img src="{{ related_car.main_image.url }}" alt="{{ related_car.title }}" class="w-full h-40 object-cover">
                        {% else %}
                            <div class="w-full h-40 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-car text-gray-400 text-3xl"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-midnight mb-2 truncate">{{ related_car.title }}</h3>
                        <p class="text-sm text-gray-600 mb-3">{{ related_car.year }} • {{ related_car.mileage|floatformat:0 }} km</p>
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-bold text-crimson">KSh {{ related_car.price|floatformat:0 }}</span>
                            <a href="{% url 'core:car_detail' related_car.pk %}" class="text-sm bg-crimson text-white px-3 py-1 rounded hover:bg-electric-red transition-colors">View</a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Inquiry Modal -->
<div id="inquiryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold">Contact Dealer</h3>
                <button onclick="closeInquiryModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form hx-post="{% url 'core:create_inquiry' %}" hx-target="#inquiry-result" hx-swap="innerHTML">
                {% csrf_token %}
                <input type="hidden" name="inquiry_type" value="car">
                <input type="hidden" name="car_id" value="{{ car.id }}">
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                        <input type="text" name="subject" value="Inquiry about {{ car.title }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-crimson focus:border-transparent" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea name="message" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-crimson focus:border-transparent" placeholder="I'm interested in this car. Please provide more details..." required></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone (Optional)</label>
                        <input type="tel" name="phone" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-crimson focus:border-transparent">
                    </div>
                </div>
                
                <div id="inquiry-result" class="mt-4"></div>
                
                <div class="flex space-x-4 mt-6">
                    <button type="button" onclick="closeInquiryModal()" class="flex-1 btn-secondary py-3">Cancel</button>
                    <button type="submit" class="flex-1 btn-primary py-3">Send Inquiry</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced image gallery functionality
    function changeMainImage(src, thumbnail) {
        document.getElementById('main-image').src = src;

        // Update thumbnail active states
        document.querySelectorAll('.thumbnail').forEach(thumb => {
            thumb.classList.remove('active');
        });

        // Add active class to clicked thumbnail
        if (thumbnail) {
            thumbnail.classList.add('active');
        }
    }

    function openInquiryModal() {
        document.getElementById('inquiryModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closeInquiryModal() {
        document.getElementById('inquiryModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    // Add to wishlist functionality
    function addToWishlist(carId) {
        // Implement wishlist functionality
        console.log('Adding car to wishlist:', carId);
        // You can implement HTMX call here
        showNotification('Added to wishlist!', 'success');
    }

    // Add to compare functionality
    function addToCompare(carId) {
        fetch(`/compare/add/${carId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                updateCompareButton(data.count);

                // Show compare link if cars added
                if (data.count > 0) {
                    setTimeout(() => {
                        const compareLink = document.createElement('div');
                        compareLink.className = 'fixed bottom-4 right-4 z-50';
                        compareLink.innerHTML = `
                            <a href="/compare/" class="bg-harrier-red text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:bg-red-600 transition-colors inline-flex items-center">
                                <i class="fas fa-balance-scale mr-2"></i>
                                Compare (${data.count})
                            </a>
                        `;
                        document.body.appendChild(compareLink);

                        // Remove after 10 seconds
                        setTimeout(() => compareLink.remove(), 10000);
                    }, 1000);
                }
            } else {
                showNotification(data.message, 'warning');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error adding car to comparison', 'error');
        });
    }



    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-lg text-white font-semibold transform translate-x-full transition-transform duration-300`;

        switch(type) {
            case 'success':
                notification.classList.add('bg-green-500');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-500');
                break;
            case 'error':
                notification.classList.add('bg-red-500');
                break;
            default:
                notification.classList.add('bg-blue-500');
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    // Update compare button with count
    function updateCompareButton(count = 0) {
        const compareButtons = document.querySelectorAll('[onclick*="addToCompare"]');

        compareButtons.forEach(button => {
            if (count > 0) {
                button.innerHTML = `<i class="fas fa-balance-scale"></i>Compare (${count})`;
            } else {
                button.innerHTML = `<i class="fas fa-balance-scale"></i>Add to Compare`;
            }
        });
    }

    // Close modal when clicking outside
    document.getElementById('inquiryModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeInquiryModal();
        }
    });

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        updateCompareButton();

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.animate-fade-in-up').forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(element);
        });
    });
</script>
{% endblock %}
